#!/usr/bin/env python3
"""
Демонстрация новой логики нумерации микротем
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.repositories.microtopic_repository import MicrotopicRepository
from database.repositories.subject_repository import SubjectRepository


async def demo_scenario():
    """Демонстрируем сценарий: было 1,2,3 → удалили 2 → стало 1,3 → добавили новые → стало 1,3,4,5,6"""
    print("🎭 ДЕМОНСТРАЦИЯ НОВОЙ ЛОГИКИ НУМЕРАЦИИ МИКРОТЕМ")
    print("=" * 60)
    
    try:
        # Получаем первый доступный предмет
        subjects = await SubjectRepository.get_all()
        if not subjects:
            print("❌ Нет предметов для демонстрации")
            return
        
        subject = subjects[0]
        subject_id = subject.id
        print(f"📚 Демонстрируем на предмете: {subject.name} (ID: {subject_id})")
        
        # Показываем исходное состояние
        print(f"\n📋 ИСХОДНОЕ СОСТОЯНИЕ:")
        current_microtopics = await MicrotopicRepository.get_by_subject(subject_id)
        for mt in current_microtopics:
            print(f"  {mt.number}. {mt.name}")
        
        # Создаем тестовые микротемы для демонстрации (если их мало)
        if len(current_microtopics) < 3:
            print(f"\n➕ Создаем тестовые микротемы для демонстрации...")
            test_names = [
                "ДЕМО Микротема 1",
                "ДЕМО Микротема 2", 
                "ДЕМО Микротема 3"
            ]
            demo_microtopics = await MicrotopicRepository.create_multiple(test_names, subject_id)
            print(f"✅ Созданы тестовые микротемы:")
            for mt in demo_microtopics:
                print(f"  {mt.number}. {mt.name}")
        
        # Обновляем список
        all_microtopics = await MicrotopicRepository.get_by_subject(subject_id)
        print(f"\n📋 СОСТОЯНИЕ ПОСЛЕ ПОДГОТОВКИ ({len(all_microtopics)} микротем):")
        for mt in all_microtopics:
            print(f"  {mt.number}. {mt.name}")
        
        # Находим микротему с номером 2 для удаления
        microtopic_to_delete = None
        for mt in all_microtopics:
            if mt.number == 2:
                microtopic_to_delete = mt
                break
        
        if microtopic_to_delete:
            print(f"\n🗑️ УДАЛЯЕМ МИКРОТЕМУ №2: '{microtopic_to_delete.name}'")
            success = await MicrotopicRepository.delete(microtopic_to_delete.id, renumber=False)
            
            if success:
                print("✅ Микротема удалена БЕЗ перенумерации")
                
                # Показываем результат
                after_delete = await MicrotopicRepository.get_by_subject(subject_id)
                print(f"\n📋 СОСТОЯНИЕ ПОСЛЕ УДАЛЕНИЯ ({len(after_delete)} микротем):")
                for mt in after_delete:
                    print(f"  {mt.number}. {mt.name}")
                
                print(f"\n💡 Как видите, номера НЕ перенумеровались!")
                print(f"   Было: 1, 2, 3, ... → Стало: 1, 3, ... (номер 2 освободился)")
                
                # Показываем доступные номера
                available = await MicrotopicRepository.get_available_numbers(subject_id, 5)
                print(f"\n🔢 Доступные номера для новых микротем: {available}")
                
                # Добавляем новые микротемы
                print(f"\n➕ ДОБАВЛЯЕМ НОВЫЕ МИКРОТЕМЫ:")
                new_names = [
                    "НОВАЯ Микротема A",
                    "НОВАЯ Микротема B",
                    "НОВАЯ Микротема C"
                ]
                
                new_microtopics = await MicrotopicRepository.create_multiple(new_names, subject_id)
                print(f"✅ Созданы новые микротемы:")
                for mt in new_microtopics:
                    print(f"  {mt.number}. {mt.name}")
                
                # Показываем финальное состояние
                final_state = await MicrotopicRepository.get_by_subject(subject_id)
                print(f"\n📋 ФИНАЛЬНОЕ СОСТОЯНИЕ ({len(final_state)} микротем):")
                for mt in final_state:
                    print(f"  {mt.number}. {mt.name}")
                
                print(f"\n🎯 РЕЗУЛЬТАТ:")
                print(f"   • Номер 2 заполнился первой новой микротемой")
                print(f"   • Остальные новые микротемы получили следующие свободные номера")
                print(f"   • Никакие существующие микротемы НЕ изменили свои номера")
                
                # Очистка: удаляем созданные для демонстрации микротемы
                print(f"\n🧹 Очищаем тестовые данные...")
                demo_keywords = ["ДЕМО", "НОВАЯ", "ТЕСТ"]
                for mt in final_state:
                    if any(keyword in mt.name for keyword in demo_keywords):
                        await MicrotopicRepository.delete(mt.id, renumber=False)
                        print(f"  🗑️ Удалена: {mt.number}. {mt.name}")
                
                print(f"\n✅ Демонстрация завершена успешно!")
            else:
                print("❌ Ошибка при удалении микротемы")
        else:
            print("❌ Не найдена микротема с номером 2 для демонстрации")
        
    except Exception as e:
        print(f"❌ Ошибка при демонстрации: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(demo_scenario())
