#!/usr/bin/env python3
"""
Тестовый скрипт для проверки новой логики нумерации микротем
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db_session
from database.repositories.microtopic_repository import MicrotopicRepository
from database.repositories.subject_repository import SubjectRepository


async def test_microtopic_numbering():
    """Тестируем новую логику нумерации микротем"""
    print("🧪 Тестирование новой логики нумерации микротем")
    print("=" * 50)
    
    try:
        # 1. Получаем первый доступный предмет
        subjects = await SubjectRepository.get_all()
        if not subjects:
            print("❌ Нет предметов для тестирования")
            return
        
        subject = subjects[0]
        subject_id = subject.id
        print(f"📚 Тестируем на предмете: {subject.name} (ID: {subject_id})")
        
        # 2. Показываем текущие микротемы
        current_microtopics = await MicrotopicRepository.get_by_subject(subject_id)
        print(f"\n📋 Текущие микротемы ({len(current_microtopics)}):")
        for mt in current_microtopics:
            print(f"  {mt.number}. {mt.name}")
        
        # 3. Тестируем получение следующего номера
        next_number = await MicrotopicRepository.get_next_number_for_subject(subject_id)
        print(f"\n🔢 Следующий свободный номер: {next_number}")
        
        # 4. Тестируем получение списка доступных номеров
        available_numbers = await MicrotopicRepository.get_available_numbers(subject_id, 5)
        print(f"📝 Доступные номера (первые 5): {available_numbers}")
        
        # 5. Создаем тестовую микротему
        test_name = f"ТЕСТ Микротема {next_number}"
        print(f"\n➕ Создаем тестовую микротему: '{test_name}'")
        
        new_microtopic = await MicrotopicRepository.create(test_name, subject_id)
        print(f"✅ Создана микротема с номером: {new_microtopic.number}")
        
        # 6. Показываем обновленный список
        updated_microtopics = await MicrotopicRepository.get_by_subject(subject_id)
        print(f"\n📋 Обновленный список микротем ({len(updated_microtopics)}):")
        for mt in updated_microtopics:
            print(f"  {mt.number}. {mt.name}")
        
        # 7. Удаляем тестовую микротему (без перенумерации)
        print(f"\n🗑️ Удаляем тестовую микротему (ID: {new_microtopic.id})")
        success = await MicrotopicRepository.delete(new_microtopic.id, renumber=False)
        
        if success:
            print("✅ Микротема удалена без перенумерации")
            
            # 8. Показываем финальный список
            final_microtopics = await MicrotopicRepository.get_by_subject(subject_id)
            print(f"\n📋 Финальный список микротем ({len(final_microtopics)}):")
            for mt in final_microtopics:
                print(f"  {mt.number}. {mt.name}")
            
            # 9. Проверяем, что номер освободился
            final_next_number = await MicrotopicRepository.get_next_number_for_subject(subject_id)
            print(f"\n🔢 Следующий свободный номер после удаления: {final_next_number}")
            
            if final_next_number == next_number:
                print("✅ Номер корректно освободился!")
            else:
                print("❌ Номер не освободился как ожидалось")
        else:
            print("❌ Ошибка при удалении микротемы")
        
        print("\n🎉 Тестирование завершено!")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()


async def test_multiple_creation():
    """Тестируем создание нескольких микротем одновременно"""
    print("\n" + "=" * 50)
    print("🧪 Тестирование массового создания микротем")
    print("=" * 50)
    
    try:
        # Получаем первый доступный предмет
        subjects = await SubjectRepository.get_all()
        if not subjects:
            print("❌ Нет предметов для тестирования")
            return
        
        subject = subjects[0]
        subject_id = subject.id
        print(f"📚 Тестируем на предмете: {subject.name} (ID: {subject_id})")
        
        # Показываем текущие микротемы
        current_microtopics = await MicrotopicRepository.get_by_subject(subject_id)
        print(f"\n📋 Текущие микротемы ({len(current_microtopics)}):")
        for mt in current_microtopics:
            print(f"  {mt.number}. {mt.name}")
        
        # Создаем несколько тестовых микротем
        test_names = [
            "ТЕСТ Множественная 1",
            "ТЕСТ Множественная 2", 
            "ТЕСТ Множественная 3"
        ]
        
        print(f"\n➕ Создаем {len(test_names)} микротем одновременно")
        new_microtopics = await MicrotopicRepository.create_multiple(test_names, subject_id)
        
        print("✅ Созданы микротемы:")
        for mt in new_microtopics:
            print(f"  {mt.number}. {mt.name}")
        
        # Удаляем тестовые микротемы
        print(f"\n🗑️ Удаляем тестовые микротемы")
        for mt in new_microtopics:
            await MicrotopicRepository.delete(mt.id, renumber=False)
            print(f"  Удалена: {mt.number}. {mt.name}")
        
        print("✅ Все тестовые микротемы удалены")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_microtopic_numbering())
    asyncio.run(test_multiple_creation())
