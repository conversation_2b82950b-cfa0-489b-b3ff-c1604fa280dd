#!/usr/bin/env python3
"""
Скрипт миграции для добавления таблиц бонусных тестов
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import init_database, get_db_session
from database.models import Base
from sqlalchemy import text


async def create_bonus_test_tables():
    """Создание таблиц для бонусных тестов"""
    print("🚀 Начинаем миграцию для бонусных тестов...")
    
    try:
        # Инициализируем базу данных (создаст все таблицы)
        await init_database()
        print("✅ Таблицы бонусных тестов созданы успешно!")
        
        # Проверяем, что таблицы созданы
        async with get_db_session() as session:
            # Проверяем таблицу bonus_tests
            result = await session.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('bonus_tests', 'bonus_questions', 'bonus_answer_options')
                ORDER BY table_name;
            """))
            tables = [row[0] for row in result.fetchall()]
            
            print(f"\n📋 Созданные таблицы бонусных тестов:")
            for table in tables:
                print(f"  ✅ {table}")
            
            if len(tables) == 3:
                print(f"\n🎉 Все таблицы бонусных тестов созданы успешно!")
            else:
                print(f"\n⚠️ Создано {len(tables)} из 3 ожидаемых таблиц")
        
        # Показываем структуру таблиц
        await show_table_structure()
        
    except Exception as e:
        print(f"❌ Ошибка при создании таблиц: {e}")
        import traceback
        traceback.print_exc()


async def show_table_structure():
    """Показать структуру созданных таблиц"""
    print(f"\n📊 Структура таблиц бонусных тестов:")
    
    async with get_db_session() as session:
        tables = ['bonus_tests', 'bonus_questions', 'bonus_answer_options']
        
        for table_name in tables:
            try:
                result = await session.execute(text(f"""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns 
                    WHERE table_name = '{table_name}' 
                    AND table_schema = 'public'
                    ORDER BY ordinal_position;
                """))
                
                columns = result.fetchall()
                print(f"\n🗂️ Таблица {table_name}:")
                for col in columns:
                    nullable = "NULL" if col[2] == "YES" else "NOT NULL"
                    default = f" DEFAULT {col[3]}" if col[3] else ""
                    print(f"  📌 {col[0]} ({col[1]}) {nullable}{default}")
                    
            except Exception as e:
                print(f"❌ Ошибка при получении структуры таблицы {table_name}: {e}")


async def test_repositories():
    """Тестирование репозиториев бонусных тестов"""
    print(f"\n🧪 Тестирование репозиториев бонусных тестов...")
    
    try:
        from database import BonusTestRepository, BonusQuestionRepository, BonusAnswerOptionRepository
        
        # Тест создания бонусного теста
        print("1️⃣ Тестируем создание бонусного теста...")
        bonus_test = await BonusTestRepository.create(
            name="ТЕСТ Демо бонусный тест",
            price=100
        )
        print(f"   ✅ Создан бонусный тест: ID={bonus_test.id}, название='{bonus_test.name}', цена={bonus_test.price}")
        
        # Тест создания вопроса
        print("2️⃣ Тестируем создание вопроса...")
        bonus_question_repo = BonusQuestionRepository()
        question = await bonus_question_repo.create(
            bonus_test_id=bonus_test.id,
            text="Какой это тестовый вопрос?",
            time_limit=60
        )
        print(f"   ✅ Создан вопрос: ID={question.id}, текст='{question.text[:30]}...', время={question.time_limit}с")
        
        # Тест создания вариантов ответов
        print("3️⃣ Тестируем создание вариантов ответов...")
        answer_options = [
            {'text': 'Первый вариант', 'is_correct': True},
            {'text': 'Второй вариант', 'is_correct': False},
            {'text': 'Третий вариант', 'is_correct': False}
        ]
        created_options = await BonusAnswerOptionRepository.create_multiple(question.id, answer_options)
        print(f"   ✅ Создано {len(created_options)} вариантов ответов")
        
        # Тест получения данных
        print("4️⃣ Тестируем получение данных...")
        retrieved_test = await BonusTestRepository.get_by_id(bonus_test.id)
        if retrieved_test:
            print(f"   ✅ Получен бонусный тест: '{retrieved_test.name}' с {len(retrieved_test.questions)} вопросами")
        
        # Очистка тестовых данных
        print("5️⃣ Очищаем тестовые данные...")
        await BonusTestRepository.delete(bonus_test.id)
        print("   ✅ Тестовые данные удалены")
        
        print(f"\n🎉 Все тесты репозиториев прошли успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании репозиториев: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """Основная функция миграции"""
    print("=" * 60)
    print("🔄 МИГРАЦИЯ БОНУСНЫХ ТЕСТОВ")
    print("=" * 60)
    
    await create_bonus_test_tables()
    await test_repositories()
    
    print("\n" + "=" * 60)
    print("✅ МИГРАЦИЯ ЗАВЕРШЕНА УСПЕШНО!")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
